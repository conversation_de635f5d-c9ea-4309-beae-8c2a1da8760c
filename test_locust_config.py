#!/usr/bin/env python3
"""
Simple test configuration for Locust to validate both AdminUser and EndUser classes.
This demonstrates how to run both user types simultaneously.
"""

from locust import HttpUser, task, between
from locustfile import AdminUser, EndUser

# Test configuration showing both user classes are available
print("Available User Classes:")
print(f"AdminUser - Abstract: {getattr(AdminUser, 'abstract', 'Not set')}")
print(f"AdminUser - Tasks: {len(getattr(AdminUser, 'tasks', {}))}")
print(f"EndUser - Abstract: {getattr(EndUser, 'abstract', 'Not set')}")  
print(f"EndUser - Tasks: {len(getattr(EndUser, 'tasks', {}))}")

# Example of how to use both classes in a locustfile
class MixedLoadTest:
    """
    Example configuration for mixed load testing.
    
    To run with both user types simultaneously, you can:
    
    1. Use the classes directly in locustfile.py (they're already configured)
    2. Or create a custom test that uses both classes
    3. Or run separate Locust instances for each user type
    
    Command examples:
    
    # Run with both user types (default behavior with current locustfile.py)
    locust -f locustfile.py --host=https://api.credexon.com
    
    # Run with specific user ratios
    locust -f locustfile.py --host=https://api.credexon.com --users 20 --spawn-rate 2
    
    # Set environment variables for credentials
    set LOGIN_EMAIL=<EMAIL>
    set LOGIN_PASSWORD=your-admin-password
    set ENDUSER_PHONE=your-phone-number
    set ENDUSER_COUNTRY_CODE=+91
    set ENDUSER_USERTYPE=2
    """
    pass

# Validate that both classes have the expected structure
def validate_user_classes():
    """Validate that both user classes are properly configured."""
    
    issues = []
    
    # Check AdminUser
    if not hasattr(AdminUser, 'tasks') or not AdminUser.tasks:
        issues.append("AdminUser has no tasks")
    elif len(AdminUser.tasks) == 0:
        issues.append("AdminUser has empty tasks dictionary")
    
    if getattr(AdminUser, 'abstract', True):
        issues.append("AdminUser is still marked as abstract")
    
    # Check EndUser
    if not hasattr(EndUser, 'tasks') or not EndUser.tasks:
        issues.append("EndUser has no tasks")
    elif len(EndUser.tasks) == 0:
        issues.append("EndUser has empty tasks dictionary")
        
    if getattr(EndUser, 'abstract', True):
        issues.append("EndUser is still marked as abstract")
    
    return issues

# Run validation
validation_issues = validate_user_classes()

if validation_issues:
    print("\n❌ Validation Issues Found:")
    for issue in validation_issues:
        print(f"  - {issue}")
else:
    print("\n✅ Both user classes are properly configured!")
    print("\nYou can now run Locust with both user types:")
    print("  locust -f locustfile.py --host=https://api.credexon.com")
    print("\nBoth AdminUser and EndUser will be available for load testing.")
    print("AdminUser will test admin endpoints (/admin/*)")
    print("EndUser will test app endpoints (/app/*)")

# Show sample tasks from each user class
print(f"\nSample AdminUser tasks:")
admin_tasks = list(AdminUser.tasks.keys())[:3]
for task in admin_tasks:
    print(f"  - {task.__name__}: {task.__doc__}")

print(f"\nSample EndUser tasks:")
end_user_tasks = list(EndUser.tasks.keys())[:3]
for task in end_user_tasks:
    print(f"  - {task.__name__}: {task.__doc__}")

print(f"\nCredentials Configuration:")
print(f"Admin credentials: {AdminUser.DEFAULT_LOGIN_EMAIL} / {AdminUser.DEFAULT_LOGIN_PASSWORD}")
print(f"EndUser auth token: {AdminUser.DEFAULT_ENDUSER_AUTH_TOKEN}")
print(f"EndUser legacy phone: {AdminUser.DEFAULT_ENDUSER_PHONE} ({AdminUser.DEFAULT_ENDUSER_COUNTRY_CODE})")
print(f"EndUser legacy type: {AdminUser.DEFAULT_ENDUSER_USERTYPE}")

print(f"\nEnvironment Variables (override defaults):")
print(f"  LOGIN_EMAIL - Admin email")
print(f"  LOGIN_PASSWORD - Admin password")
print(f"  ENDUSER_AUTH_TOKEN - End user bearer token (NEW - simplified auth)")

print(f"\nEndUser Authentication Changes:")
print(f"  ✅ NEW: Token-based authentication via ENDUSER_AUTH_TOKEN")
print(f"  ✅ Bypasses phone/OTP verification complexity")
print(f"  ✅ Fallback: Operates without auth if no token provided")
print(f"  ❌ REMOVED: Phone/OTP login flow (/app/v1/users/login)")

# Test token-based authentication logic
import os
print(f"\nTesting Token Authentication Logic:")

# Test 1: No token
if 'ENDUSER_AUTH_TOKEN' in os.environ:
    del os.environ['ENDUSER_AUTH_TOKEN']
auth_token = os.getenv("ENDUSER_AUTH_TOKEN", AdminUser.DEFAULT_ENDUSER_AUTH_TOKEN)
print(f"  No token provided: {'✅ Fallback mode' if not auth_token else '❌ Unexpected token'}")

# Test 2: Token provided
test_token = "test_bearer_token_12345"
os.environ['ENDUSER_AUTH_TOKEN'] = test_token
auth_token = os.getenv("ENDUSER_AUTH_TOKEN", AdminUser.DEFAULT_ENDUSER_AUTH_TOKEN)
print(f"  Token provided: {'✅ Token detected: ' + auth_token[:10] + '...' if auth_token else '❌ Token not detected'}")

# Clean up
if 'ENDUSER_AUTH_TOKEN' in os.environ:
    del os.environ['ENDUSER_AUTH_TOKEN']

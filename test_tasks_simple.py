#!/usr/bin/env python3
"""
Simple test to verify tasks are properly attached and discoverable by <PERSON><PERSON><PERSON>.
"""

import locustfile
from locust.user.task import get_tasks_from_base_classes

def test_task_discovery():
    """Test if <PERSON><PERSON><PERSON> can discover the tasks properly."""
    
    print("=== TESTING TASK DISCOVERY ===")
    
    # Get the SwaggerApiUser class
    user_class = locustfile.SwaggerApiUser
    print(f"User class: {user_class}")
    print(f"Abstract: {getattr(user_class, 'abstract', None)}")
    
    # Check tasks using Locust's internal method
    try:
        tasks = get_tasks_from_base_classes([user_class], {})
        print(f"Tasks discovered by <PERSON>cust: {len(tasks)}")
        
        if tasks:
            print("First 5 tasks:")
            for i, (task_func, weight) in enumerate(list(tasks.items())[:5]):
                print(f"  {i+1}. {task_func.__name__}: weight={weight}")
        else:
            print("No tasks discovered!")
            
    except Exception as e:
        print(f"Error discovering tasks: {e}")
    
    # Check the tasks attribute directly
    tasks_attr = getattr(user_class, 'tasks', [])
    print(f"Tasks attribute: {tasks_attr}")
    
    # Check for task methods manually
    task_methods = []
    for attr_name in dir(user_class):
        if attr_name.startswith('task_'):
            attr = getattr(user_class, attr_name)
            if callable(attr) and hasattr(attr, 'locust_task_weight'):
                task_methods.append((attr_name, attr.locust_task_weight))
    
    print(f"Manual task method discovery: {len(task_methods)}")
    if task_methods:
        print("First 5 manual tasks:")
        for i, (name, weight) in enumerate(task_methods[:5]):
            print(f"  {i+1}. {name}: weight={weight}")
    
    # Try to create an instance and check its tasks
    try:
        print("\n=== TESTING INSTANCE CREATION ===")
        # Don't actually create an instance as it will try to connect
        # Just check the class structure
        
        # Check if the class has the required attributes
        required_attrs = ['host', 'wait_time']
        for attr in required_attrs:
            if hasattr(user_class, attr):
                print(f"✓ Has {attr}: {getattr(user_class, attr)}")
            else:
                print(f"✗ Missing {attr}")
                
    except Exception as e:
        print(f"Error checking instance: {e}")

if __name__ == "__main__":
    test_task_discovery()

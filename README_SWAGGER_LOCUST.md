# Enhanced Swagger-Based Locust Load Testing

This enhanced `locustfile.py` automatically generates comprehensive load tests based on your Swagger/OpenAPI specification file (`swagger.json`). It eliminates the need for manual test creation and adapts automatically to API changes.

## 🚀 Features

### ✅ Automatic Test Generation
- **Dynamic Task Creation**: Automatically creates Locust tasks for every API endpoint in your Swagger specification
- **Schema-Based Data Generation**: Generates realistic test data based on parameter definitions and request/response schemas
- **Intelligent Weighting**: Assigns appropriate task weights based on endpoint usage patterns (higher weight for list/read operations)

### ✅ Comprehensive HTTP Support
- **All HTTP Methods**: Supports GET, POST, PUT, DELETE, PATCH operations
- **Request Body Handling**: Properly formats JSON and form-encoded request payloads
- **Path Parameters**: Automatically replaces path parameters with dynamic or generated values
- **Query Parameters**: Handles query string parameters from schema definitions

### ✅ Smart Authentication
- **Bearer Token Support**: Automatically detects and handles Bearer authentication from Swagger security schemes
- **Auto Re-authentication**: Automatically re-authenticates when tokens expire
- **Configurable Credentials**: Uses environment variables for flexible credential management

### ✅ Advanced Data Management
- **Dynamic Data Extraction**: Extracts IDs and other useful data from API responses
- **Data Reuse**: Reuses extracted data for subsequent requests (e.g., using created IDs for update/delete operations)
- **Realistic Test Data**: Uses Faker library to generate contextually appropriate test data

### ✅ Robust Error Handling
- **Response Categorization**: Properly categorizes different types of failures (auth, validation, server errors)
- **Expected Validation Errors**: Handles validation errors gracefully (expected with random test data)
- **Detailed Logging**: Provides comprehensive logging for debugging and monitoring

## 📋 Generated Test Statistics

From your current `swagger.json`:
- **255 API endpoints** automatically discovered
- **255 dynamic tasks** generated
- **Bearer authentication** configured
- **2 server environments** detected (localhost and live)

## 🛠️ Installation

The enhanced locustfile requires additional dependencies for Swagger parsing and test data generation:

```bash
# Install additional dependencies
uv add pydantic jsonschema faker openapi-spec-validator

# Or with pip
pip install pydantic jsonschema faker openapi-spec-validator
```

## 🚀 Usage

### Basic Load Testing
```bash
# Start Locust with web UI
locust -f locustfile.py --host=https://preapi.credexon.com

# Access web UI at http://localhost:8089
```

### Headless Load Testing
```bash
# Run headless test with 10 users, spawn rate 2/sec, for 60 seconds
locust -f locustfile.py --host=https://preapi.credexon.com -u 10 -r 2 --headless -t 60s
```

### Custom Authentication
```bash
# Windows
set LOGIN_EMAIL=<EMAIL>
set LOGIN_PASSWORD=your-password
locust -f locustfile.py --host=https://preapi.credexon.com

# Linux/Mac
export LOGIN_EMAIL=<EMAIL>
export LOGIN_PASSWORD=your-password
locust -f locustfile.py --host=https://preapi.credexon.com
```

## ⚙️ Configuration

### Environment Variables
- `LOGIN_EMAIL`: Override default login email (default: <EMAIL>)
- `LOGIN_PASSWORD`: Override default login password (default: LvYFJfVw*q)

### File Configuration
- `swagger.json`: Swagger/OpenAPI specification file (must be in current directory)
- Host URL is automatically detected from Swagger servers configuration

## 📊 Task Weighting Strategy

The system automatically assigns weights to endpoints based on expected usage patterns:

| Endpoint Type | Weight | Examples |
|---------------|--------|----------|
| List/Read (GET) | 10 | `/admin/v1/banner/list` |
| Other GET | 5 | `/admin/v1/banner/view` |
| Create (POST) | 5 | `/admin/v1/banner/create` |
| View/Filter (POST) | 3 | `/admin/v1/banner/filter` |
| Other POST | 2 | Various POST endpoints |
| Update (PUT/PATCH) | 2 | `/admin/v1/banner/update` |
| Delete | 1 | `/admin/v1/banner/delete` |

## 🔍 Monitoring and Debugging

### Response Categories
- **✅ Success (200-202)**: Request completed successfully
- **🔄 Auth Failure (401)**: Automatic re-authentication attempted
- **⚠️ Validation Error (400, 422)**: Expected with random test data
- **❌ Other Errors**: Logged for investigation

### Logging Levels
- **INFO**: Task generation, authentication success
- **DEBUG**: Individual request success/failure details
- **WARNING**: Authentication failures, unexpected errors

## 🧪 Testing the Setup

Run the included test script to verify everything is working:

```bash
python test_swagger_parsing.py
```

This will:
- ✅ Verify swagger.json can be loaded
- ✅ Count discovered endpoints
- ✅ Test dynamic task generation
- ✅ Validate authentication configuration
- ✅ Show example generated tasks

## 🔄 Updating API Tests

To update tests when your API changes:
1. Update your `swagger.json` file
2. Restart Locust - tasks are regenerated automatically
3. No code changes required!

## 🎯 Best Practices

1. **Start Small**: Begin with low user counts to verify authentication and basic functionality
2. **Monitor Logs**: Watch for authentication failures or unexpected errors
3. **Use Realistic Data**: The system generates realistic test data, but you may want to customize for specific scenarios
4. **Environment Separation**: Use different credentials for different environments
5. **Gradual Scaling**: Increase load gradually to identify bottlenecks

## 🔧 Customization

The enhanced locustfile is designed to work out-of-the-box, but you can customize:

- **Test Data Generation**: Modify `generate_*_data` methods for specific data requirements
- **Authentication**: Extend authentication handling for different auth schemes
- **Response Handling**: Customize response processing and data extraction
- **Task Weights**: Adjust the weighting algorithm for your specific use case

## 📈 Performance Benefits

- **Zero Manual Maintenance**: Tests update automatically with API changes
- **Comprehensive Coverage**: Every endpoint is tested automatically
- **Realistic Load Patterns**: Intelligent weighting simulates real usage
- **Efficient Data Usage**: Dynamic data extraction reduces test data dependencies

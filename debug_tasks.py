#!/usr/bin/env python3
"""
Debug script to check what tasks are being created on the user classes.
"""

import locustfile

def debug_user_classes():
    """Debug the user classes and their tasks."""
    
    print("=== DEBUGGING USER CLASSES ===")
    
    # Check SwaggerApiUser
    print(f"\n1. SwaggerApiUser class: {locustfile.SwaggerApiUser}")
    print(f"   Type: {type(locustfile.SwaggerApiUser)}")
    print(f"   MRO: {locustfile.SwaggerApiUser.__mro__}")
    
    # Check if it has abstract attribute
    abstract_attr = getattr(locustfile.SwaggerApiUser, 'abstract', None)
    print(f"   Abstract: {abstract_attr}")
    
    # List all task methods
    task_methods = [method for method in dir(locustfile.SwaggerApiUser) if method.startswith('task_')]
    print(f"   Task methods count: {len(task_methods)}")
    
    if task_methods:
        print("   First 5 task methods:")
        for i, method_name in enumerate(task_methods[:5]):
            method = getattr(locustfile.SwaggerApiUser, method_name)
            print(f"     {i+1}. {method_name}: {method}")
            print(f"        Callable: {callable(method)}")
            print(f"        Has task decorator: {hasattr(method, 'locust_task_weight')}")
            if hasattr(method, 'locust_task_weight'):
                print(f"        Task weight: {method.locust_task_weight}")
    
    # Check the base classes
    print(f"\n2. SwaggerBasedApiUser class: {locustfile.SwaggerBasedApiUser}")
    abstract_attr = getattr(locustfile.SwaggerBasedApiUser, 'abstract', None)
    print(f"   Abstract: {abstract_attr}")
    base_task_methods = [method for method in dir(locustfile.SwaggerBasedApiUser) if method.startswith('task_')]
    print(f"   Task methods count: {len(base_task_methods)}")
    
    print(f"\n3. AdminApiUser class: {locustfile.AdminApiUser}")
    abstract_attr = getattr(locustfile.AdminApiUser, 'abstract', None)
    print(f"   Abstract: {abstract_attr}")
    admin_task_methods = [method for method in dir(locustfile.AdminApiUser) if method.startswith('task_')]
    print(f"   Task methods count: {len(admin_task_methods)}")
    
    # Check if there are any other user classes
    print(f"\n4. All classes in locustfile module:")
    for name in dir(locustfile):
        obj = getattr(locustfile, name)
        if isinstance(obj, type) and hasattr(obj, '__mro__'):
            # Check if it's a subclass of HttpUser
            try:
                from locust import HttpUser
                if issubclass(obj, HttpUser):
                    print(f"   {name}: {obj}")
                    abstract_attr = getattr(obj, 'abstract', None)
                    print(f"     Abstract: {abstract_attr}")
                    task_count = len([method for method in dir(obj) if method.startswith('task_')])
                    print(f"     Task methods: {task_count}")
            except:
                pass

if __name__ == "__main__":
    debug_user_classes()

# Dual User Type Load Testing with Locust

This enhanced version of the Swagger-based Locust load testing framework now supports **two distinct user types** for realistic mixed load testing scenarios.

## 🎯 Overview

The system now provides:

- **AdminUser**: Tests admin panel endpoints (`/admin/*`) with vendor authentication
- **EndUser**: Tests app/frontend endpoints (`/app/*`) with user authentication  
- **Mixed Load Testing**: Both user types can run simultaneously
- **Automatic Endpoint Filtering**: Each user type only executes relevant APIs
- **Dynamic Task Generation**: 240+ endpoints automatically converted to test tasks

## 📊 Endpoint Distribution

From the Swagger specification:
- **Total Endpoints**: 255
- **Admin Endpoints** (`/admin/*`): 126 endpoints
- **App Endpoints** (`/app/*`): 114 endpoints  
- **Other Endpoints**: 15 endpoints

## 🔐 Authentication Patterns

### AdminUser Authentication
- **Endpoint**: `/admin/v1/vendor/login`
- **Method**: POST with JSON payload
- **Credentials**: Email + Password
- **Token**: Bearer token in `Authorization` header
- **Default Credentials**: `<EMAIL>` / `LvYFJfVw*q`

### EndUser Authentication
- **Method**: Simplified token-based authentication
- **Token Source**: Environment variable `ENDUSER_AUTH_TOKEN`
- **Fallback**: No authentication (for endpoints that don't require it)
- **Header**: `Authorization: Bearer <token>` (when token provided)
- **Benefits**: Bypasses phone/OTP verification complexity

## 🚀 Usage

### Basic Usage
```bash
# Run with both user types (default)
locust -f locustfile.py --host=https://api.credexon.com

# Run with specific user counts and spawn rate
locust -f locustfile.py --host=https://api.credexon.com --users 20 --spawn-rate 2
```

### Environment Variables
Override default credentials using environment variables:

```bash
# Admin credentials
set LOGIN_EMAIL=<EMAIL>
set LOGIN_PASSWORD=your-admin-password

# End user authentication token (optional)
set ENDUSER_AUTH_TOKEN=your-bearer-token-here

# Then run Locust
locust -f locustfile.py --host=https://api.credexon.com
```

### PowerShell (Windows)
```powershell
$env:LOGIN_EMAIL="<EMAIL>"
$env:LOGIN_PASSWORD="your-admin-password"
$env:ENDUSER_AUTH_TOKEN="your-bearer-token-here"
locust -f locustfile.py --host=https://api.credexon.com
```

## 🏗️ Architecture

### Class Hierarchy
```
SwaggerBasedApiUser (Abstract Base Class)
├── AdminUser (Concrete - Admin endpoints)
└── EndUser (Concrete - App endpoints)
```

### Key Features

1. **Endpoint Filtering**: Each user class automatically filters endpoints based on URL patterns
2. **Dynamic Task Generation**: Tasks are generated at import time from Swagger spec
3. **Intelligent Weighting**: Endpoints weighted based on expected usage patterns
4. **Auto Re-authentication**: Handles 401 responses by re-authenticating
5. **Schema-based Data Generation**: Uses Swagger schemas to generate realistic test data

## 📋 Sample Tasks

### AdminUser Tasks (126 total)
- `task_post__admin_v1_banner_save`: Create a new banner
- `task_get__admin_v1_banner_list`: Get list of banners  
- `task_post__admin_v1_category_create`: Create a new category
- `task_get__admin_v1_users_list`: Get list of users
- `task_post__admin_v1_vendor_login`: Authenticate a vendor

### EndUser Tasks (114 total)
- `task_post__app_v1_banners_list`: Get list of banners
- `task_post__app_v1_users_login`: User login (front-end)
- `task_post__app_v1_pool_join_pool`: Join a pool contest
- `task_post__app_v1_match_live_score`: Get live cricket match score
- `task_post__app_v1_users_get_profile`: Get user profile by user ID

## 🧪 Testing & Validation

### Run Tests
```bash
# Test endpoint filtering and task generation
python test_user_classes.py

# Validate Locust configuration  
python test_locust_config.py
```

### Expected Results
- ✅ AdminUser: 126 tasks for `/admin/*` endpoints
- ✅ EndUser: 114 tasks for `/app/*` endpoints
- ✅ Both classes marked as non-abstract
- ✅ Dynamic task generation working
- ✅ Credential configuration working

## 🔑 EndUser Simplified Authentication

The EndUser class now uses a **simplified token-based authentication** approach that bypasses the complexity of phone/OTP verification:

### How It Works
1. **Token from Environment**: Set `ENDUSER_AUTH_TOKEN` environment variable with a valid bearer token
2. **Automatic Headers**: When token is provided, sets `Authorization: Bearer <token>` header
3. **Fallback Mode**: If no token provided, operates without authentication (for endpoints that don't require it)
4. **No API Calls**: Skips the `/app/v1/users/login` endpoint entirely

### Getting a Token
To get a valid token for testing:
1. Use your app's login flow manually to obtain a token
2. Extract the bearer token from the response
3. Set it as an environment variable: `ENDUSER_AUTH_TOKEN=your-token-here`

### Benefits
- ✅ **Simplified Setup**: No need to handle phone/OTP verification
- ✅ **Faster Testing**: Skips login API calls during load testing
- ✅ **Flexible**: Works with or without authentication
- ✅ **Realistic**: Uses actual production tokens

## 🔧 Configuration

### Default Credentials
```python
# Admin User
DEFAULT_LOGIN_EMAIL = "<EMAIL>"
DEFAULT_LOGIN_PASSWORD = "LvYFJfVw*q"

# End User (Token-based)
DEFAULT_ENDUSER_AUTH_TOKEN = None  # No default token - uses fallback
```

### Locust Settings
```python
wait_time = between(1, 5)  # 1-5 seconds between requests
host = "https://api.credexon.com"  # Default API host
```

## 📈 Load Testing Scenarios

### Scenario 1: Mixed User Load
- 70% EndUser traffic (app/frontend usage)
- 30% AdminUser traffic (admin panel usage)
- Simulates realistic production load

### Scenario 2: Admin-Heavy Load  
- Test admin panel performance under heavy load
- Focus on admin endpoints only

### Scenario 3: App-Heavy Load
- Test frontend/mobile app performance  
- Focus on app endpoints only

## 🔍 Monitoring & Debugging

### Logging
The system provides detailed logging:
- Authentication success/failure
- Endpoint filtering results
- Task generation statistics
- Request/response handling

### Task Inspection
```python
# View generated tasks
print(f"AdminUser tasks: {len(AdminUser.tasks)}")
print(f"EndUser tasks: {len(EndUser.tasks)}")

# Sample task names
admin_tasks = list(AdminUser.tasks.keys())[:5]
for task in admin_tasks:
    print(f"Admin: {task.__name__}")
```

## 🚨 Important Notes

1. **Simultaneous Execution**: Both user types run simultaneously by default
2. **Endpoint Isolation**: AdminUser only hits `/admin/*`, EndUser only hits `/app/*`
3. **Authentication Handling**:
   - AdminUser: Full login flow with `/admin/v1/vendor/login`
   - EndUser: Simplified token-based auth via `ENDUSER_AUTH_TOKEN`
4. **Error Handling**: 401 responses trigger automatic re-authentication (AdminUser only)
5. **Data Generation**: Uses Faker and Swagger schemas for realistic test data
6. **EndUser Fallback**: If no token provided, EndUser operates without authentication

## 🎉 Benefits

- **Realistic Testing**: Simulates actual user behavior patterns
- **Comprehensive Coverage**: Tests both admin and user-facing functionality
- **Automated Setup**: No manual endpoint configuration required
- **Flexible Configuration**: Easy credential and environment management
- **Scalable**: Supports high concurrent user loads for both user types

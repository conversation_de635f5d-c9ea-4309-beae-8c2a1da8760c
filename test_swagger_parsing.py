#!/usr/bin/env python3
"""
Test script to verify the enhanced locustfile.py can properly parse swagger.json
and generate dynamic tasks.
"""

import json
import sys
import os

def test_swagger_parsing():
    """Test if swagger.json can be parsed and tasks can be generated."""
    
    print("Testing Swagger-based Locust file...")
    
    # Check if swagger.json exists
    if not os.path.exists("swagger.json"):
        print("❌ swagger.json file not found!")
        return False
    
    # Try to load swagger.json
    try:
        with open("swagger.json", 'r', encoding='utf-8') as f:
            swagger_spec = json.load(f)
        print("✅ Successfully loaded swagger.json")
    except Exception as e:
        print(f"❌ Failed to load swagger.json: {e}")
        return False
    
    # Check if paths exist
    if 'paths' not in swagger_spec:
        print("❌ No 'paths' found in swagger specification")
        return False
    
    paths_count = len(swagger_spec['paths'])
    print(f"✅ Found {paths_count} paths in swagger specification")
    
    # Count endpoints
    endpoint_count = 0
    for path, path_item in swagger_spec['paths'].items():
        for method, operation in path_item.items():
            if method.lower() in ['get', 'post', 'put', 'delete', 'patch']:
                endpoint_count += 1
    
    print(f"✅ Found {endpoint_count} API endpoints")
    
    # Try to import the locustfile
    try:
        import locustfile
        print("✅ Successfully imported locustfile.py")
    except Exception as e:
        print(f"❌ Failed to import locustfile.py: {e}")
        return False
    
    # Check if SwaggerApiUser class exists
    if hasattr(locustfile, 'SwaggerApiUser'):
        print("✅ SwaggerApiUser class found")
        
        # Check if dynamic tasks were created
        swagger_user_class = locustfile.SwaggerApiUser
        task_methods = [method for method in dir(swagger_user_class) if method.startswith('task_')]
        print(f"✅ Generated {len(task_methods)} dynamic task methods")
        
        # Show some example tasks
        if task_methods:
            print("\n📋 Example generated tasks:")
            for i, task_method in enumerate(task_methods[:5]):  # Show first 5 tasks
                method_obj = getattr(swagger_user_class, task_method)
                doc = getattr(method_obj, '__doc__', 'No description')
                print(f"  {i+1}. {task_method}: {doc}")
            
            if len(task_methods) > 5:
                print(f"  ... and {len(task_methods) - 5} more tasks")
    else:
        print("❌ SwaggerApiUser class not found")
        return False
    
    # Check if authentication configuration is detected
    security_schemes = swagger_spec.get('components', {}).get('securitySchemes', {})
    if security_schemes:
        print(f"✅ Found security schemes: {list(security_schemes.keys())}")
    else:
        print("⚠️  No security schemes found in swagger specification")
    
    # Check servers configuration
    servers = swagger_spec.get('servers', [])
    if servers:
        print(f"✅ Found {len(servers)} server configurations:")
        for server in servers:
            print(f"  - {server.get('url', 'No URL')} ({server.get('description', 'No description')})")
    else:
        print("⚠️  No server configurations found")
    
    print("\n🎉 All tests passed! The enhanced locustfile.py is ready to use.")
    return True

def show_usage_instructions():
    """Show instructions on how to use the enhanced locustfile."""
    print("\n" + "="*60)
    print("📖 USAGE INSTRUCTIONS")
    print("="*60)
    print("""
The enhanced locustfile.py now automatically generates load tests from swagger.json!

🚀 To run load tests:

1. Basic usage:
   locust -f locustfile.py --host=https://preapi.credexon.com

2. With custom user count and spawn rate:
   locust -f locustfile.py --host=https://preapi.credexon.com -u 10 -r 2

3. Headless mode (no web UI):
   locust -f locustfile.py --host=https://preapi.credexon.com -u 10 -r 2 --headless -t 60s

4. With custom login credentials (via environment variables):
   set LOGIN_EMAIL=<EMAIL>
   set LOGIN_PASSWORD=your-password
   locust -f locustfile.py --host=https://preapi.credexon.com

🔧 Features:
- ✅ Automatically parses swagger.json to generate API tests
- ✅ Generates realistic test data based on schema definitions
- ✅ Handles authentication (Bearer tokens)
- ✅ Supports all HTTP methods (GET, POST, PUT, DELETE, PATCH)
- ✅ Intelligent endpoint weighting (more load on list endpoints)
- ✅ Dynamic data extraction and reuse (IDs from responses)
- ✅ Proper error handling and response validation
- ✅ Configurable via environment variables

📝 Configuration:
- Swagger file: swagger.json (in current directory)
- Default host: https://preapi.credexon.com (from swagger servers)
- Default login: <EMAIL> / LvYFJfVw*q
- Override with LOGIN_EMAIL and LOGIN_PASSWORD environment variables

🔍 Monitoring:
- Check Locust web UI at http://localhost:8089 (when not in headless mode)
- Monitor logs for detailed request/response information
- Failed requests are categorized (auth failures, validation errors, etc.)
""")

if __name__ == "__main__":
    success = test_swagger_parsing()
    
    if success:
        show_usage_instructions()
        sys.exit(0)
    else:
        print("\n❌ Tests failed. Please check the errors above.")
        sys.exit(1)

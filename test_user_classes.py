#!/usr/bin/env python3
"""
Test script to validate the AdminUser and EndUser classes.
This script tests endpoint filtering and basic functionality without making actual HTTP requests.
"""

import json
import sys
from locustfile import Admin<PERSON>ser, EndUser, SwaggerBasedApi<PERSON>ser

def test_endpoint_filtering():
    """Test that endpoint filtering works correctly for both user classes."""
    
    print("Testing endpoint filtering...")
    
    # Load swagger spec
    try:
        with open("swagger.json", "r", encoding="utf-8") as f:
            swagger_spec = json.load(f)
    except FileNotFoundError:
        print("ERROR: swagger.json not found!")
        return False
    
    # Count total endpoints
    total_endpoints = 0
    admin_endpoints = 0
    app_endpoints = 0
    other_endpoints = 0
    
    if "paths" in swagger_spec:
        for path, path_item in swagger_spec["paths"].items():
            for method, operation in path_item.items():
                if method.lower() in ["get", "post", "put", "delete", "patch"]:
                    total_endpoints += 1
                    if path.startswith("/admin/"):
                        admin_endpoints += 1
                    elif path.startswith("/app/"):
                        app_endpoints += 1
                    else:
                        other_endpoints += 1
    
    print(f"Total endpoints found: {total_endpoints}")
    print(f"Admin endpoints (/admin/*): {admin_endpoints}")
    print(f"App endpoints (/app/*): {app_endpoints}")
    print(f"Other endpoints: {other_endpoints}")
    
    return True

def test_user_class_instantiation():
    """Test that user classes can be instantiated and have correct properties."""
    
    print("\nTesting user class instantiation...")
    
    try:
        # Test AdminUser
        print("Creating AdminUser instance...")
        admin_user = AdminUser()
        print(f"AdminUser abstract: {getattr(admin_user, 'abstract', 'Not set')}")
        print(f"AdminUser has filter method: {hasattr(admin_user, 'filter_endpoints_by_user_type')}")
        print(f"AdminUser has login method: {hasattr(admin_user, 'login')}")
        
        # Test EndUser
        print("Creating EndUser instance...")
        end_user = EndUser()
        print(f"EndUser abstract: {getattr(end_user, 'abstract', 'Not set')}")
        print(f"EndUser has filter method: {hasattr(end_user, 'filter_endpoints_by_user_type')}")
        print(f"EndUser has login method: {hasattr(end_user, 'login')}")
        
        # Test base class
        print("Testing base SwaggerBasedApiUser...")
        print(f"SwaggerBasedApiUser abstract: {getattr(SwaggerBasedApiUser, 'abstract', 'Not set')}")
        
        return True
        
    except Exception as e:
        print(f"ERROR during instantiation: {e}")
        return False

def test_task_generation():
    """Test that tasks are generated correctly for each user class."""
    
    print("\nTesting task generation...")
    
    try:
        # Check AdminUser tasks
        admin_tasks = getattr(AdminUser, 'tasks', {})
        print(f"AdminUser has {len(admin_tasks)} tasks")
        
        # Check EndUser tasks
        end_user_tasks = getattr(EndUser, 'tasks', {})
        print(f"EndUser has {len(end_user_tasks)} tasks")
        
        # Show some sample task names
        if admin_tasks:
            sample_admin_tasks = list(admin_tasks.keys())[:5]
            print(f"Sample AdminUser tasks: {[task.__name__ for task in sample_admin_tasks]}")
        
        if end_user_tasks:
            sample_end_user_tasks = list(end_user_tasks.keys())[:5]
            print(f"Sample EndUser tasks: {[task.__name__ for task in sample_end_user_tasks]}")
        
        return True
        
    except Exception as e:
        print(f"ERROR during task generation test: {e}")
        return False

def test_credentials_configuration():
    """Test that credentials are properly configured."""
    
    print("\nTesting credentials configuration...")
    
    try:
        # Test AdminUser credentials
        admin_user = AdminUser()
        print(f"Admin default email: {admin_user.DEFAULT_LOGIN_EMAIL}")
        print(f"Admin default password: {admin_user.DEFAULT_LOGIN_PASSWORD}")
        
        # Test EndUser credentials
        end_user = EndUser()
        print(f"EndUser default phone: {end_user.DEFAULT_ENDUSER_PHONE}")
        print(f"EndUser default country code: {end_user.DEFAULT_ENDUSER_COUNTRY_CODE}")
        print(f"EndUser default usertype: {end_user.DEFAULT_ENDUSER_USERTYPE}")
        
        return True
        
    except Exception as e:
        print(f"ERROR during credentials test: {e}")
        return False

def main():
    """Run all tests."""
    
    print("=" * 60)
    print("TESTING ADMINUSER AND ENDUSER CLASSES")
    print("=" * 60)
    
    tests = [
        test_endpoint_filtering,
        test_user_class_instantiation,
        test_task_generation,
        test_credentials_configuration,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
                print(f"✓ {test.__name__} PASSED")
            else:
                failed += 1
                print(f"✗ {test.__name__} FAILED")
        except Exception as e:
            failed += 1
            print(f"✗ {test.__name__} FAILED with exception: {e}")
        
        print("-" * 40)
    
    print(f"\nTest Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed!")
        return 0
    else:
        print("❌ Some tests failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
